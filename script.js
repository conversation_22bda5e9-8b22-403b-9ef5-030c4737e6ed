/* =============================
   script.js
============================= */
document.addEventListener("DOMContentLoaded", () => {
  document.getElementById("contactForm").addEventListener("submit", function(e){
    e.preventDefault();
    alert("Thank you for reaching out! I'll get back to you soon.");
    this.reset();
  });

  // Smooth scroll effect
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener("click", function(e) {
      e.preventDefault();
      document.querySelector(this.getAttribute("href")).scrollIntoView({
        behavior: "smooth"
      });
    });
  });
});
