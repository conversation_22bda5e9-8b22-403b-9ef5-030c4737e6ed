/* =============================
   Modern Portfolio JavaScript
   Enhanced with animations and interactions
============================= */

// ===== GLOBAL VARIABLES =====
let isLoading = true;
let currentTheme = localStorage.getItem('theme') || 'light';
let typingIndex = 0;
let typingTextIndex = 0;
const typingTexts = [
  'Full-Stack Developer',
  'Problem Solver',
  'B.Tech CSE Student',
  'Hackathon Winner',
  'Code Enthusiast'
];

// ===== INITIALIZATION =====
document.addEventListener("DOMContentLoaded", () => {
  initializeApp();
});

function initializeApp() {
  // Initialize all features
  initializeLoading();
  initializeTheme();
  initializeNavigation();
  initializeTypingEffect();
  initializeScrollEffects();
  initializeAnimations();
  initializeFormHandling();
  initializeSkillBars();
  initializeCounters();

  // Initialize AOS (Animate On Scroll)
  if (typeof AOS !== 'undefined') {
    AOS.init({
      duration: 1000,
      once: true,
      offset: 100,
      easing: 'ease-out-cubic'
    });
  }
}

// ===== LOADING SCREEN =====
function initializeLoading() {
  const loadingScreen = document.getElementById('loading-screen');

  // Simulate loading time
  setTimeout(() => {
    loadingScreen.classList.add('hidden');
    isLoading = false;

    // Remove loading screen after animation
    setTimeout(() => {
      loadingScreen.style.display = 'none';
    }, 500);
  }, 2000);
}

// ===== THEME TOGGLE =====
function initializeTheme() {
  const themeToggle = document.getElementById('theme-toggle');
  const body = document.body;

  // Set initial theme
  body.setAttribute('data-theme', currentTheme);
  updateThemeIcon();

  themeToggle.addEventListener('click', () => {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    body.setAttribute('data-theme', currentTheme);
    localStorage.setItem('theme', currentTheme);
    updateThemeIcon();
  });
}

function updateThemeIcon() {
  const themeToggle = document.getElementById('theme-toggle');
  const icon = themeToggle.querySelector('i');

  if (currentTheme === 'dark') {
    icon.className = 'fas fa-sun';
  } else {
    icon.className = 'fas fa-moon';
  }
}

// ===== NAVIGATION =====
function initializeNavigation() {
  const hamburger = document.getElementById('hamburger');
  const navLinks = document.querySelector('.nav-links');
  const header = document.querySelector('.header');

  // Mobile menu toggle
  hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navLinks.classList.toggle('active');
  });

  // Close mobile menu when clicking on a link
  document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', () => {
      hamburger.classList.remove('active');
      navLinks.classList.remove('active');
    });
  });

  // Header scroll effect
  window.addEventListener('scroll', () => {
    if (window.scrollY > 100) {
      header.classList.add('scrolled');
    } else {
      header.classList.remove('scrolled');
    }
  });

  // Active navigation highlighting
  updateActiveNavigation();
  window.addEventListener('scroll', updateActiveNavigation);
}

function updateActiveNavigation() {
  const sections = document.querySelectorAll('section[id]');
  const navLinks = document.querySelectorAll('.nav-link');

  let currentSection = '';

  sections.forEach(section => {
    const sectionTop = section.offsetTop - 150;
    const sectionHeight = section.offsetHeight;

    if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
      currentSection = section.getAttribute('id');
    }
  });

  navLinks.forEach(link => {
    link.classList.remove('active');
    if (link.getAttribute('href') === `#${currentSection}`) {
      link.classList.add('active');
    }
  });
}
// ===== TYPING EFFECT =====
function initializeTypingEffect() {
  const typingElement = document.getElementById('typing-text');
  if (!typingElement) return;

  function typeText() {
    const currentText = typingTexts[typingTextIndex];

    if (typingIndex < currentText.length) {
      typingElement.textContent = currentText.substring(0, typingIndex + 1);
      typingIndex++;
      setTimeout(typeText, 100);
    } else {
      setTimeout(eraseText, 2000);
    }
  }

  function eraseText() {
    const currentText = typingTexts[typingTextIndex];

    if (typingIndex > 0) {
      typingElement.textContent = currentText.substring(0, typingIndex - 1);
      typingIndex--;
      setTimeout(eraseText, 50);
    } else {
      typingTextIndex = (typingTextIndex + 1) % typingTexts.length;
      setTimeout(typeText, 500);
    }
  }

  // Start typing effect
  setTimeout(typeText, 1000);
}

// ===== SCROLL EFFECTS =====
function initializeScrollEffects() {
  initializeScrollProgress();
  initializeBackToTop();
  initializeSmoothScrolling();
}

function initializeScrollProgress() {
  const scrollProgress = document.querySelector('.scroll-progress');

  window.addEventListener('scroll', () => {
    const scrollTop = window.scrollY;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    const scrollPercent = (scrollTop / docHeight) * 100;

    scrollProgress.style.width = scrollPercent + '%';
  });
}

function initializeBackToTop() {
  const backToTop = document.getElementById('backToTop');

  window.addEventListener('scroll', () => {
    if (window.scrollY > 500) {
      backToTop.classList.add('visible');
    } else {
      backToTop.classList.remove('visible');
    }
  });

  backToTop.addEventListener('click', () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  });
}

function initializeSmoothScrolling() {
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));

      if (target) {
        const headerHeight = document.querySelector('.header').offsetHeight;
        const targetPosition = target.offsetTop - headerHeight;

        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      }
    });
  });
}

// ===== ANIMATIONS =====
function initializeAnimations() {
  initializeIntersectionObserver();
  initializeParallaxEffect();
}

function initializeIntersectionObserver() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');

        // Trigger skill bars animation
        if (entry.target.classList.contains('skills')) {
          animateSkillBars();
        }

        // Trigger counters animation
        if (entry.target.classList.contains('about')) {
          animateCounters();
        }
      }
    });
  }, observerOptions);

  // Observe sections
  document.querySelectorAll('section').forEach(section => {
    observer.observe(section);
  });
}

function initializeParallaxEffect() {
  const shapes = document.querySelectorAll('.shape');

  window.addEventListener('scroll', () => {
    const scrolled = window.scrollY;
    const rate = scrolled * -0.5;

    shapes.forEach((shape, index) => {
      const speed = (index + 1) * 0.3;
      shape.style.transform = `translateY(${rate * speed}px)`;
    });
  });
}

// ===== SKILL BARS =====
function initializeSkillBars() {
  // This will be triggered by intersection observer
}

function animateSkillBars() {
  const skillBars = document.querySelectorAll('.skill-progress');

  skillBars.forEach(bar => {
    const width = bar.getAttribute('data-width');
    setTimeout(() => {
      bar.style.width = width + '%';
    }, 300);
  });
}

// ===== COUNTERS =====
function initializeCounters() {
  // This will be triggered by intersection observer
}

function animateCounters() {
  const counters = document.querySelectorAll('.stat-number');

  counters.forEach(counter => {
    const target = parseInt(counter.getAttribute('data-count'));
    const duration = 2000;
    const increment = target / (duration / 16);
    let current = 0;

    const updateCounter = () => {
      current += increment;
      if (current < target) {
        counter.textContent = Math.floor(current);
        requestAnimationFrame(updateCounter);
      } else {
        counter.textContent = target;
      }
    };

    updateCounter();
  });
}
// ===== FORM HANDLING =====
function initializeFormHandling() {
  const contactForm = document.getElementById('contactForm');
  if (!contactForm) return;

  contactForm.addEventListener('submit', handleFormSubmit);

  // Add floating label effect
  const formGroups = document.querySelectorAll('.form-group');
  formGroups.forEach(group => {
    const input = group.querySelector('input, textarea');
    const label = group.querySelector('label');

    if (input && label) {
      input.addEventListener('focus', () => {
        group.classList.add('focused');
      });

      input.addEventListener('blur', () => {
        if (!input.value) {
          group.classList.remove('focused');
        }
      });

      // Check if input has value on load
      if (input.value) {
        group.classList.add('focused');
      }
    }
  });
}

function handleFormSubmit(e) {
  e.preventDefault();

  const form = e.target;
  const formData = new FormData(form);
  const submitButton = form.querySelector('button[type="submit"]');

  // Show loading state
  const originalText = submitButton.innerHTML;
  submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
  submitButton.disabled = true;

  // Simulate form submission (replace with actual form handling)
  setTimeout(() => {
    // Show success message
    showNotification('Thank you for your message! I\'ll get back to you soon.', 'success');

    // Reset form
    form.reset();

    // Reset button
    submitButton.innerHTML = originalText;
    submitButton.disabled = false;

    // Remove focused class from form groups
    document.querySelectorAll('.form-group').forEach(group => {
      group.classList.remove('focused');
    });
  }, 2000);
}

// ===== NOTIFICATIONS =====
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
      <span>${message}</span>
      <button class="notification-close">
        <i class="fas fa-times"></i>
      </button>
    </div>
  `;

  // Add styles
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${type === 'success' ? '#10b981' : '#6366f1'};
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 400px;
  `;

  document.body.appendChild(notification);

  // Animate in
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
  }, 100);

  // Close button functionality
  const closeButton = notification.querySelector('.notification-close');
  closeButton.addEventListener('click', () => {
    removeNotification(notification);
  });

  // Auto remove after 5 seconds
  setTimeout(() => {
    removeNotification(notification);
  }, 5000);
}

function removeNotification(notification) {
  notification.style.transform = 'translateX(100%)';
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 300);
}

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
}

// ===== PERFORMANCE OPTIMIZATIONS =====
// Optimize scroll events
const optimizedScrollHandler = throttle(() => {
  // Handle scroll events here if needed
}, 16);

window.addEventListener('scroll', optimizedScrollHandler);

// ===== ACCESSIBILITY ENHANCEMENTS =====
function initializeAccessibility() {
  // Add keyboard navigation for custom elements
  document.querySelectorAll('.btn, .social-link, .project-link').forEach(element => {
    element.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        element.click();
      }
    });
  });

  // Add focus indicators
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Tab') {
      document.body.classList.add('keyboard-navigation');
    }
  });

  document.addEventListener('mousedown', () => {
    document.body.classList.remove('keyboard-navigation');
  });
}

// Initialize accessibility features
document.addEventListener('DOMContentLoaded', initializeAccessibility);

// ===== ERROR HANDLING =====
window.addEventListener('error', (e) => {
  console.error('JavaScript Error:', e.error);
  // You can add error reporting here
});

// ===== LAZY LOADING =====
function initializeLazyLoading() {
  const images = document.querySelectorAll('img[data-src]');

  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove('lazy');
        imageObserver.unobserve(img);
      }
    });
  });

  images.forEach(img => imageObserver.observe(img));
}

// Initialize lazy loading if there are lazy images
if (document.querySelectorAll('img[data-src]').length > 0) {
  initializeLazyLoading();
}

// ===== CONSOLE EASTER EGG =====
console.log(`
%c🚀 Welcome to Kaushal's Portfolio! 🚀
%cLooking for a developer? You found one!
%cFeel free to explore the code and reach out if you'd like to work together.

%c📧 Email: <EMAIL>
%c💼 LinkedIn: linkedin.com/in/kaushalrajpandey
%c🐙 GitHub: github.com/kaushalrajpandey

%cBuilt with ❤️ using HTML, CSS, and JavaScript
`,
'color: #6366f1; font-size: 16px; font-weight: bold;',
'color: #10b981; font-size: 14px;',
'color: #f59e0b; font-size: 12px;',
'color: #ef4444; font-size: 12px;',
'color: #8b5cf6; font-size: 12px;',
'color: #06b6d4; font-size: 12px;',
'color: #84cc16; font-size: 12px;'
);
