
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background: #f8f9fc;
  color: #333;
  line-height: 1.6;
}

header {
  background: #0d6efd;
  color: #fff;
  padding: 15px 30px;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.7rem;
  font-weight: bold;
  letter-spacing: 1px;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: 20px;
}

.nav-links a {
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  transition: 0.3s;
}

.nav-links a:hover {
  color: #ffdd57;
}

.hero {
  text-align: center;
  padding: 100px 20px;
  background: linear-gradient(135deg, #0d6efd, #00b4d8);
  color: #fff;
  animation: fadeIn 1.5s ease-in;
}

.hero h1 {
  font-size: 2.8rem;
}

.hero h1 span {
  color: #ffdd57;
}

.btn {
  display: inline-block;
  margin-top: 15px;
  background: #ffdd57;
  color: #333;
  padding: 10px 20px;
  border-radius: 5px;
  text-decoration: none;
  transition: 0.3s;
}

.btn:hover {
  background: #ffc107;
}

section {
  padding: 60px 20px;
  max-width: 1100px;
  margin: auto;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #0d6efd;
  font-size: 2rem;
  position: relative;
}

h2::after {
  content: '';
  width: 60px;
  height: 4px;
  background: #0d6efd;
  display: block;
  margin: 10px auto;
  border-radius: 2px;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.skill, .project-card {
  background: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  text-align: center;
  transition: transform 0.3s;
}

.project-card:hover {
  transform: translateY(-5px);
}

.achievements ul {
  list-style: disc;
  padding-left: 40px;
  font-size: 1.1rem;
}

.contact form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 500px;
  margin: auto;
}

.contact input, .contact textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 1rem;
}

footer {
  text-align: center;
  padding: 10px;
  background: #0d6efd;
  color: #fff;
  font-size: 0.9rem;
}

@keyframes fadeIn {
  0% {opacity:0;transform:translateY(-20px);}
  100% {opacity:1;transform:translateY(0);}
}

