
/* ===== CSS VARIABLES ===== */
:root {
  /* Vibrant Color Palette */
  --primary-color: #ff6b6b;
  --primary-dark: #ee5a52;
  --primary-light: #ff8e8e;
  --secondary-color: #4ecdc4;
  --accent-color: #45b7d1;
  --accent-secondary: #96ceb4;
  --accent-tertiary: #feca57;
  --accent-quaternary: #ff9ff3;
  --text-primary: #2c3e50;
  --text-secondary: #34495e;
  --text-light: #7f8c8d;
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-dark: #1a1a2e;
  --bg-darker: #16213e;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(255, 107, 107, 0.1);
  --shadow-medium: 0 8px 25px rgba(255, 107, 107, 0.15);
  --shadow-large: 0 15px 35px rgba(255, 107, 107, 0.2);
  --shadow-xl: 0 25px 50px rgba(255, 107, 107, 0.25);
  --shadow-colored: 0 10px 30px rgba(78, 205, 196, 0.3);

  /* Gradient Variables */
  --gradient-primary: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
  --gradient-secondary: linear-gradient(135deg, #45b7d1 0%, #96ceb4 100%);
  --gradient-tertiary: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
  --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  --gradient-card: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  --gradient-text: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  --gradient-glow: radial-gradient(circle, rgba(255,107,107,0.3) 0%, transparent 70%);

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-base: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* Dark theme variables */
[data-theme="dark"] {
  --text-primary: #f8f9fa;
  --text-secondary: #dee2e6;
  --text-light: #adb5bd;
  --bg-primary: #1a1a2e;
  --bg-secondary: #16213e;
  --bg-dark: #0f0f23;
  --bg-darker: #0a0a1a;
  --border-color: #495057;
  --gradient-hero: linear-gradient(135deg, #2c3e50 0%, #3498db 50%, #9b59b6 100%);
  --gradient-card: linear-gradient(145deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%);
}
/* ===== CUSTOM CURSOR ===== */
.cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--gradient-primary);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
}

.cursor-follower {
  position: fixed;
  width: 40px;
  height: 40px;
  background: rgba(255, 107, 107, 0.2);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  transition: transform 0.3s ease;
  border: 2px solid rgba(255, 107, 107, 0.5);
}

.cursor-hover {
  transform: scale(2);
}

.cursor-follower-hover {
  transform: scale(1.5);
  background: rgba(78, 205, 196, 0.3);
  border-color: rgba(78, 205, 196, 0.7);
}

/* ===== MAGNETIC HOVER EFFECTS ===== */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic:hover {
  transform: scale(1.05);
}

/* ===== TILT EFFECTS ===== */
.tilt {
  transition: transform 0.3s ease;
  transform-style: preserve-3d;
}

/* ===== GLOW EFFECTS ===== */
.glow-on-hover {
  position: relative;
  overflow: hidden;
}

.glow-on-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-glow);
  transition: left 0.5s ease;
  z-index: -1;
}

.glow-on-hover:hover::before {
  left: 100%;
}

/* ===== PARTICLE SYSTEM ===== */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: float-particle 6s infinite linear;
}

.particle:nth-child(odd) {
  background: rgba(255, 107, 107, 0.6);
  animation-duration: 8s;
}

.particle:nth-child(3n) {
  background: rgba(78, 205, 196, 0.6);
  animation-duration: 10s;
}

/* ===== MORPHING SHAPES ===== */
.morphing-shape {
  position: absolute;
  border-radius: 50%;
  background: var(--gradient-primary);
  opacity: 0.1;
  animation: morph 8s infinite ease-in-out;
}

.morphing-shape:nth-child(2) {
  animation-delay: 2s;
  background: var(--gradient-secondary);
}

.morphing-shape:nth-child(3) {
  animation-delay: 4s;
  background: var(--gradient-tertiary);
}

/* ===== INTERACTIVE ELEMENTS ===== */
.interactive-card {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-style: preserve-3d;
}

.interactive-card:hover {
  transform: translateY(-10px) rotateX(5deg);
  box-shadow: var(--shadow-xl);
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:hover::after {
  width: 300px;
  height: 300px;
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  overflow-x: hidden;
  transition: background-color var(--transition-base), color var(--transition-base);
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.section {
  padding: var(--space-20) 0;
}

.text-center {
  text-align: center;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== LOADING SCREEN ===== */
#loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

#loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loader {
  text-align: center;
}

.loader-text {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.loader-text span {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  color: white;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loader-text span:nth-child(1) { animation-delay: -0.32s; }
.loader-text span:nth-child(2) { animation-delay: -0.16s; }

.loader-bar {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.loader-bar::after {
  content: '';
  display: block;
  width: 40%;
  height: 100%;
  background: white;
  border-radius: var(--radius-full);
  animation: loading 2s infinite ease-in-out;
}

/* ===== SCROLL PROGRESS ===== */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  z-index: var(--z-fixed);
  transition: width 0.1s ease;
}

/* ===== THEME TOGGLE ===== */
.theme-toggle {
  position: fixed;
  top: var(--space-6);
  right: var(--space-6);
  width: 50px;
  height: 50px;
  border: none;
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-medium);
  cursor: pointer;
  z-index: var(--z-fixed);
  transition: all var(--transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-large);
}

/* ===== HEADER & NAVIGATION ===== */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: var(--z-sticky);
  transition: all var(--transition-base);
}

.header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-medium);
}

.navbar {
  padding: var(--space-4) 0;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.logo {
  display: flex;
  flex-direction: column;
  text-decoration: none;
  color: var(--text-primary);
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
}

.logo-subtitle {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: -2px;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: var(--space-8);
  margin: 0;
}

.nav-link {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 500;
  font-size: var(--font-size-sm);
  position: relative;
  transition: color var(--transition-base);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: width var(--transition-base);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* ===== HAMBURGER MENU ===== */
.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  transition: all var(--transition-base);
  border-radius: var(--radius-sm);
}

.hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: var(--gradient-hero);
  overflow: hidden;
  padding-top: 80px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

.hero-container {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.hero-content {
  color: white;
}

.hero-greeting {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-4);
  opacity: 0.9;
}

.wave {
  animation: wave 2s infinite;
  transform-origin: 70% 70%;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--space-4);
}

.name-highlight {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-6);
  min-height: 40px;
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.typing-text {
  color: var(--secondary-color);
  font-weight: 600;
}

.cursor {
  animation: blink 1s infinite;
  color: var(--secondary-color);
}

.hero-description {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  margin-bottom: var(--space-8);
  opacity: 0.9;
  max-width: 500px;
}

.hero-buttons {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.hero-social {
  display: flex;
  gap: var(--space-4);
}

.social-link {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all var(--transition-base);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-container {
  position: relative;
  width: 350px;
  height: 350px;
}

.image-bg {
  position: absolute;
  top: -20px;
  left: -20px;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
  border-radius: var(--radius-xl);
  animation: pulse 3s infinite;
}

.profile-image {
  position: relative;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-xl);
  border: 4px solid rgba(255, 255, 255, 0.2);
  z-index: 2;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1));
  border-radius: var(--radius-xl);
  z-index: 3;
}

.scroll-indicator {
  position: absolute;
  bottom: var(--space-8);
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  opacity: 0.8;
}

.scroll-text {
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-2);
}

.scroll-arrow {
  animation: bounce-vertical 2s infinite;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background-size: 200% 200%;
  animation: glow-pulse 3s ease-in-out infinite alternate;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-colored);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-large);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.btn-full {
  width: 100%;
  justify-content: center;
}

/* ===== SECTION STYLING ===== */
.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* ===== ABOUT SECTION ===== */
.about {
  background: var(--bg-primary);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: start;
}

.about-intro h3 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.about-intro p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: var(--space-4);
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-6);
  margin: var(--space-8) 0;
}

.stat-item {
  text-align: center;
  padding: var(--space-6);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.stat-number {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  color: var(--primary-color);
  display: block;
  margin-bottom: var(--space-2);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.about-buttons {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-6);
}

.about-skills h4 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
}

.skill-categories {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.skill-category {
  padding: var(--space-6);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
}

.skill-category:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.category-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-4);
}

.skill-category h5 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.skill-category p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* ===== SKILLS SECTION ===== */
.skills {
  background: var(--bg-secondary);
}

.skills-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
}

.skills-categories .skill-category {
  background: var(--bg-primary);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
}

.skills-categories .skill-category:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-large);
}

.category-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.category-header h3 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.skill-item {
  margin-bottom: var(--space-4);
}

.skill-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.skill-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.skill-percentage {
  font-weight: 600;
  color: var(--primary-color);
  font-size: var(--font-size-sm);
}

.skill-bar {
  height: 8px;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  border-radius: var(--radius-full);
  width: 0%;
  transition: width 1.5s ease-in-out;
  position: relative;
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

/* ===== PROJECTS SECTION ===== */
.projects {
  background: var(--bg-primary);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.project-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
  position: relative;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
}

.project-image {
  height: 200px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  position: relative;
  overflow: hidden;
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-base);
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-links {
  display: flex;
  gap: var(--space-4);
}

.project-link {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all var(--transition-base);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.project-link:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.project-content {
  padding: var(--space-6);
}

.project-category {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--primary-color);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: var(--space-2);
}

.project-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.project-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.tech-tag {
  padding: var(--space-1) var(--space-3);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 500;
  border: 1px solid var(--border-color);
}

.projects-cta {
  text-align: center;
  padding: var(--space-8);
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-color);
}

.projects-cta p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
}

/* ===== ACHIEVEMENTS TIMELINE ===== */
.achievements {
  background: var(--bg-secondary);
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--space-12);
  display: flex;
  align-items: center;
}

.timeline-item:nth-child(odd) {
  flex-direction: row-reverse;
}

.timeline-item:nth-child(odd) .timeline-content {
  text-align: right;
  margin-right: var(--space-8);
}

.timeline-item:nth-child(even) .timeline-content {
  margin-left: var(--space-8);
}

.timeline-marker {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-lg);
  box-shadow: var(--shadow-medium);
  z-index: 2;
}

.timeline-content {
  flex: 1;
  background: var(--bg-primary);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
}

.timeline-content:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-large);
}

.timeline-date {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--space-2);
}

.timeline-content h3 {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.timeline-content p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-3);
}

.achievement-badge {
  display: inline-block;
  padding: var(--space-1) var(--space-3);
  background: linear-gradient(135deg, var(--secondary-color), #fbbf24);
  color: white;
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

/* ===== CONTACT SECTION ===== */
.contact {
  background: var(--bg-primary);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
}

.contact-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-lg);
}

.contact-details h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.contact-details p {
  color: var(--text-secondary);
}

.contact-social {
  margin-top: var(--space-6);
}

.contact-social h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.social-links {
  display: flex;
  gap: var(--space-3);
}

.contact-social .social-link {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.contact-social .social-link:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}
/* ===== FORM STYLING ===== */
.form {
  background: var(--bg-secondary);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-medium);
}

.form-group {
  position: relative;
  margin-bottom: var(--space-6);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--space-4);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-base);
  outline: none;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group label {
  position: absolute;
  top: var(--space-4);
  left: var(--space-4);
  color: var(--text-light);
  font-size: var(--font-size-base);
  pointer-events: none;
  transition: all var(--transition-base);
}

.form-group input:focus + label,
.form-group textarea:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group textarea:not(:placeholder-shown) + label {
  top: -10px;
  left: var(--space-3);
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  background: var(--bg-secondary);
  padding: 0 var(--space-2);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
  background: var(--bg-dark);
  color: white;
  padding: var(--space-12) 0 var(--space-6);
  position: relative;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
}

.footer-text p {
  margin-bottom: var(--space-2);
  color: rgba(255, 255, 255, 0.8);
}

.footer-text i {
  color: #ef4444;
  animation: heartbeat 1.5s infinite;
}

.footer-links {
  display: flex;
  gap: var(--space-6);
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-base);
}

.footer-links a:hover {
  color: var(--primary-light);
}

.back-to-top {
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  box-shadow: var(--shadow-large);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  z-index: var(--z-fixed);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

/* ===== ANIMATIONS ===== */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(250%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes wave {
  0%, 100% {
    transform: rotate(0deg);
  }
  10%, 30% {
    transform: rotate(14deg);
  }
  20% {
    transform: rotate(-8deg);
  }
  40% {
    transform: rotate(-4deg);
  }
  50% {
    transform: rotate(10deg);
  }
  60% {
    transform: rotate(0deg);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce-vertical {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes float-particle {
  0% {
    transform: translateY(100vh) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes morph {
  0%, 100% {
    border-radius: 50% 50% 50% 50%;
    transform: rotate(0deg) scale(1);
  }
  25% {
    border-radius: 60% 40% 60% 40%;
    transform: rotate(90deg) scale(1.1);
  }
  50% {
    border-radius: 40% 60% 40% 60%;
    transform: rotate(180deg) scale(0.9);
  }
  75% {
    border-radius: 70% 30% 70% 30%;
    transform: rotate(270deg) scale(1.05);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 107, 107, 0.6), 0 0 60px rgba(78, 205, 196, 0.3);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

@keyframes magnetic-pull {
  0% {
    transform: translateX(0) translateY(0);
  }
  100% {
    transform: translateX(var(--mouse-x, 0)) translateY(var(--mouse-y, 0));
  }
}

@keyframes color-wave {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
  }

  .hero-image {
    order: -1;
  }

  .image-container {
    width: 300px;
    height: 300px;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .timeline::before {
    left: 30px;
  }

  .timeline-item {
    flex-direction: row !important;
  }

  .timeline-item .timeline-content {
    margin-left: var(--space-16) !important;
    margin-right: 0 !important;
    text-align: left !important;
  }

  .timeline-marker {
    left: 30px !important;
    transform: translateX(-50%);
  }
}

@media (max-width: 768px) {
  .nav-links {
    position: fixed;
    top: 80px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 80px);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: var(--space-12);
    transition: left var(--transition-base);
    z-index: var(--z-dropdown);
  }

  .nav-links.active {
    left: 0;
  }

  .nav-links li {
    margin-bottom: var(--space-6);
  }

  .nav-link {
    font-size: var(--font-size-lg);
  }

  .hamburger {
    display: flex;
  }

  .theme-toggle {
    top: var(--space-4);
    right: var(--space-4);
    width: 40px;
    height: 40px;
  }

  .hero {
    padding-top: 100px;
    min-height: auto;
    padding-bottom: var(--space-16);
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-social {
    justify-content: center;
  }

  .about-stats {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .about-buttons {
    flex-direction: column;
  }

  .skills-categories {
    grid-template-columns: 1fr;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    gap: var(--space-6);
    text-align: center;
  }

  .footer-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-3);
  }

  .section {
    padding: var(--space-16) 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: var(--font-size-base);
  }

  .image-container {
    width: 250px;
    height: 250px;
  }

  .section-title {
    font-size: 2rem;
  }

  .timeline::before {
    left: 20px;
  }

  .timeline-marker {
    left: 20px !important;
    width: 40px;
    height: 40px;
    font-size: var(--font-size-base);
  }

  .timeline-item .timeline-content {
    margin-left: var(--space-12) !important;
  }

  .back-to-top {
    width: 40px;
    height: 40px;
    bottom: var(--space-4);
    right: var(--space-4);
  }
}

/* ===== DARK MODE STYLES ===== */
[data-theme="dark"] .header {
  background: rgba(31, 41, 55, 0.95);
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group textarea {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .theme-toggle i::before {
  content: "\f185"; /* sun icon */
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .header,
  .theme-toggle,
  .back-to-top,
  .scroll-progress,
  #loading-screen {
    display: none !important;
  }

  .section {
    page-break-inside: avoid;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
