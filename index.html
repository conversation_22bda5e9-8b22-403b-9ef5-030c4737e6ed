<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON> | Portfolio</title>
  <link rel="stylesheet" href="style.css">
</head>

<body>
  <header>
    <nav>
      <div class="logo"><PERSON><PERSON><PERSON></div>
      <ul class="nav-links">
        <li><a href="#about">About</a></li>
        <li><a href="#skills">Skills</a></li>
        <li><a href="#projects">Projects</a></li>
        <li><a href="#achievements">Achievements</a></li>
        <li><a href="#contact">Contact</a></li>
      </ul>
    </nav>
  </header>

  <section class="hero">
    <div class="hero-text">
      <h1>Hi, I'm <span><PERSON><PERSON><PERSON></span></h1>
      <p>B.Tech CSE | Full-Stack Developer | Problem Solver</p>
      <a href="#contact" class="btn">Hire Me</a>
    </div>
  </section>

  <section id="about" class="about">
    <h2>About Me</h2>
    <p>I am a B.Tech Computer Science student at Heritage Institute of Technology, Kolkata, passionate about solving real-world problems through code. With strong foundations in DSA (300+ questions solved) and experience in both frontend and backend development, I build user-centric applications that are functional and scalable. I actively participate in hackathons and have developed multiple impactful projects using MERN stack, Flask, Firebase, and APIs.</p>
  </section>

  <section id="skills" class="skills">
    <h2>Skills</h2>
    <div class="skills-grid">
      <div class="skill">C, C++, Java, JavaScript, Python</div>
      <div class="skill">HTML, CSS, Bootstrap, Tailwind CSS</div>
      <div class="skill">React.js, Node.js, Express.js</div>
      <div class="skill">MongoDB, Firebase, Cloudinary</div>
      <div class="skill">REST APIs</div>
      <div class="skill">Flask, Selenium, Gemini LLM</div>
    </div>
  </section>

  <section id="projects" class="projects">
    <h2>Projects</h2>

    <div class="project-card">
      <h3>City Connect</h3>
      <p>A full-stack problem-reporting platform where users can upload pictures and descriptions of local issues. Users can comment, upvote, or downvote. Built with HTML, CSS, JS, Python (Flask), and MongoDB.</p>
    </div>

    <div class="project-card">
      <h3>Airbnb Clone</h3>
      <p>Full-stack Airbnb-like platform featuring listing creation, image uploads via Cloudinary, user authentication, and booking flow. Built using Node.js, Express.js, MongoDB, and EJS templates.</p>
    </div>

    <div class="project-card">
      <h3>Trip Zennie</h3>
      <p>Travel assistant platform that auto-generates a complete trip plan (route, hotels, transport) based on 2-3 inputs. Built with Flask, Selenium, JavaScript, and Gemini LLM.</p>
    </div>

    <div class="project-card">
      <h3>Sahyog</h3>
      <p>Doctor appointment booking system integrating Google Calendar API and Firebase. Offers a sleek UI with Tailwind CSS and real-time sync of booking data. Secured 3rd position in Postman API Hackathon (BITS Pilani).</p>
    </div>

  </section>

  <section id="achievements" class="achievements">
    <h2>Achievements</h2>
    <ul>
      <li>3rd Place – Postman API Hackathon (BITS Pilani)</li>
      <li>Qualified 2 Rounds – Hack Heritage Hackathon</li>
      <li>300+ DSA questions solved (LeetCode, CodeStudio, etc.)</li>
      <li>Attended SSB Conferences – NDA Allahabad (33), Bhopal (21)</li>
    </ul>
  </section>

  <section id="contact" class="contact">
    <h2>Contact</h2>
    <form id="contactForm">
      <input type="text" placeholder="Your Name" required>
      <input type="email" placeholder="Your Email" required>
      <textarea placeholder="Your Message" required></textarea>
      <button type="submit" class="btn">Send Message</button>
    </form>
  </section>

  <footer>
    <p>© 2025 Kaushal Raj Pandey | All Rights Reserved</p>
  </footer>

  <script src="script.js"></script>
</body>

</html>
