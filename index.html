<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<PERSON><PERSON><PERSON> - Full-Stack Developer & B.Tech CSE Student. Passionate about solving real-world problems through code.">
  <meta name="keywords" content="Full-Stack Developer, Web Developer, React, Node.js, MongoDB, Portfolio">
  <meta name="author" content="<PERSON><PERSON>al <PERSON>">
  <meta property="og:title" content="<PERSON><PERSON><PERSON> Pandey | Portfolio">
  <meta property="og:description" content="Full-Stack Developer & B.Tech CSE Student passionate about solving real-world problems through code.">
  <meta property="og:type" content="website">

  <title><PERSON><PERSON><PERSON> | Full-Stack Developer Portfolio</title>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <link rel="stylesheet" href="style.css">
</head>

<body>
  <!-- Loading Screen -->
  <div id="loading-screen">
    <div class="loader">
      <div class="loader-text">
        <span>K</span><span>R</span><span>P</span>
      </div>
      <div class="loader-bar"></div>
    </div>
  </div>

  <!-- Scroll Progress Indicator -->
  <div class="scroll-progress"></div>

  <!-- Theme Toggle -->
  <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
    <i class="fas fa-moon"></i>
  </button>

  <!-- Navigation -->
  <header class="header">
    <nav class="navbar">
      <div class="nav-container">
        <div class="logo">
          <span class="logo-text">Kaushal Raj Pandey</span>
          <span class="logo-subtitle">Full-Stack Developer</span>
        </div>

        <ul class="nav-links">
          <li><a href="#home" class="nav-link">Home</a></li>
          <li><a href="#about" class="nav-link">About</a></li>
          <li><a href="#skills" class="nav-link">Skills</a></li>
          <li><a href="#projects" class="nav-link">Projects</a></li>
          <li><a href="#achievements" class="nav-link">Achievements</a></li>
          <li><a href="#contact" class="nav-link">Contact</a></li>
        </ul>

        <div class="hamburger" id="hamburger">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>
  </header>

  <!-- Hero Section -->
  <section id="home" class="hero">
    <div class="hero-background">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
      </div>
    </div>

    <div class="hero-container">
      <div class="hero-content" data-aos="fade-up" data-aos-duration="1000">
        <div class="hero-greeting">
          <span class="wave">👋</span>
          <span>Hello, I'm</span>
        </div>

        <h1 class="hero-title">
          <span class="name-highlight">Kaushal Raj Pandey</span>
        </h1>

        <div class="hero-subtitle">
          <span class="typing-text" id="typing-text"></span>
          <span class="cursor">|</span>
        </div>

        <p class="hero-description" data-aos="fade-up" data-aos-delay="300">
          B.Tech CSE Student passionate about creating innovative solutions through code.
          Specialized in full-stack development with 300+ DSA problems solved.
        </p>

        <div class="hero-buttons" data-aos="fade-up" data-aos-delay="500">
          <a href="#contact" class="btn btn-primary">
            <i class="fas fa-envelope"></i>
            Hire Me
          </a>
          <a href="#projects" class="btn btn-secondary">
            <i class="fas fa-code"></i>
            View Work
          </a>
        </div>

        <div class="hero-social" data-aos="fade-up" data-aos-delay="700">
          <a href="#" class="social-link" aria-label="LinkedIn">
            <i class="fab fa-linkedin-in"></i>
          </a>
          <a href="#" class="social-link" aria-label="GitHub">
            <i class="fab fa-github"></i>
          </a>
          <a href="#" class="social-link" aria-label="Twitter">
            <i class="fab fa-twitter"></i>
          </a>
          <a href="#" class="social-link" aria-label="Email">
            <i class="fas fa-envelope"></i>
          </a>
        </div>
      </div>

      <div class="hero-image" data-aos="fade-left" data-aos-delay="400">
        <div class="image-container">
          <div class="image-bg"></div>
          <img src="assets/WIN_20240930_18_02_31_Pro.jpg" alt="Kaushal Raj Pandey" class="profile-image">
          <div class="image-overlay"></div>
        </div>
      </div>
    </div>

    <div class="scroll-indicator" data-aos="fade-up" data-aos-delay="1000">
      <div class="scroll-text">Scroll Down</div>
      <div class="scroll-arrow">
        <i class="fas fa-chevron-down"></i>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="about section">
    <div class="container">
      <div class="section-header" data-aos="fade-up">
        <h2 class="section-title">About Me</h2>
        <p class="section-subtitle">Get to know me better</p>
      </div>

      <div class="about-content">
        <div class="about-text" data-aos="fade-right">
          <div class="about-intro">
            <h3>Hello! I'm Kaushal Raj Pandey</h3>
            <p>
              I am a B.Tech Computer Science student at Heritage Institute of Technology, Kolkata,
              passionate about solving real-world problems through code. With strong foundations in
              DSA (300+ questions solved) and experience in both frontend and backend development,
              I build user-centric applications that are functional and scalable.
            </p>
            <p>
              I actively participate in hackathons and have developed multiple impactful projects
              using MERN stack, Flask, Firebase, and APIs. My goal is to create technology that
              makes a positive impact on people's lives.
            </p>
          </div>

          <div class="about-stats">
            <div class="stat-item">
              <div class="stat-number" data-count="300">0</div>
              <div class="stat-label">DSA Problems Solved</div>
            </div>
            <div class="stat-item">
              <div class="stat-number" data-count="10">0</div>
              <div class="stat-label">Projects Completed</div>
            </div>
            <div class="stat-item">
              <div class="stat-number" data-count="2">0</div>
              <div class="stat-label">Hackathon Wins</div>
            </div>
          </div>

          <div class="about-buttons">
            <a href="#contact" class="btn btn-primary">
              <i class="fas fa-download"></i>
              Download CV
            </a>
            <a href="#projects" class="btn btn-outline">
              <i class="fas fa-eye"></i>
              View Projects
            </a>
          </div>
        </div>

        <div class="about-skills" data-aos="fade-left">
          <h4>What I Do</h4>
          <div class="skill-categories">
            <div class="skill-category">
              <div class="category-icon">
                <i class="fas fa-code"></i>
              </div>
              <h5>Frontend Development</h5>
              <p>Creating responsive and interactive user interfaces using modern frameworks and libraries.</p>
            </div>

            <div class="skill-category">
              <div class="category-icon">
                <i class="fas fa-server"></i>
              </div>
              <h5>Backend Development</h5>
              <p>Building robust server-side applications and APIs with scalable architecture.</p>
            </div>

            <div class="skill-category">
              <div class="category-icon">
                <i class="fas fa-database"></i>
              </div>
              <h5>Database Design</h5>
              <p>Designing efficient database schemas and optimizing query performance.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Skills Section -->
  <section id="skills" class="skills section">
    <div class="container">
      <div class="section-header" data-aos="fade-up">
        <h2 class="section-title">Skills & Technologies</h2>
        <p class="section-subtitle">Technologies I work with</p>
      </div>

      <div class="skills-content">
        <div class="skills-categories">
          <!-- Programming Languages -->
          <div class="skill-category" data-aos="fade-up" data-aos-delay="100">
            <div class="category-header">
              <div class="category-icon">
                <i class="fas fa-code"></i>
              </div>
              <h3>Programming Languages</h3>
            </div>
            <div class="skills-list">
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">JavaScript</span>
                  <span class="skill-percentage">90%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="90"></div>
                </div>
              </div>
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">Python</span>
                  <span class="skill-percentage">85%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="85"></div>
                </div>
              </div>
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">Java</span>
                  <span class="skill-percentage">80%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="80"></div>
                </div>
              </div>
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">C++</span>
                  <span class="skill-percentage">75%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="75"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Frontend Technologies -->
          <div class="skill-category" data-aos="fade-up" data-aos-delay="200">
            <div class="category-header">
              <div class="category-icon">
                <i class="fas fa-palette"></i>
              </div>
              <h3>Frontend</h3>
            </div>
            <div class="skills-list">
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">React.js</span>
                  <span class="skill-percentage">88%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="88"></div>
                </div>
              </div>
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">HTML/CSS</span>
                  <span class="skill-percentage">95%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="95"></div>
                </div>
              </div>
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">Tailwind CSS</span>
                  <span class="skill-percentage">85%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="85"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Backend Technologies -->
          <div class="skill-category" data-aos="fade-up" data-aos-delay="300">
            <div class="category-header">
              <div class="category-icon">
                <i class="fas fa-server"></i>
              </div>
              <h3>Backend</h3>
            </div>
            <div class="skills-list">
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">Node.js</span>
                  <span class="skill-percentage">85%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="85"></div>
                </div>
              </div>
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">Express.js</span>
                  <span class="skill-percentage">80%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="80"></div>
                </div>
              </div>
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">Flask</span>
                  <span class="skill-percentage">75%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="75"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Database & Tools -->
          <div class="skill-category" data-aos="fade-up" data-aos-delay="400">
            <div class="category-header">
              <div class="category-icon">
                <i class="fas fa-database"></i>
              </div>
              <h3>Database & Tools</h3>
            </div>
            <div class="skills-list">
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">MongoDB</span>
                  <span class="skill-percentage">80%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="80"></div>
                </div>
              </div>
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">Firebase</span>
                  <span class="skill-percentage">75%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="75"></div>
                </div>
              </div>
              <div class="skill-item">
                <div class="skill-info">
                  <span class="skill-name">REST APIs</span>
                  <span class="skill-percentage">85%</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="85"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Projects Section -->
  <section id="projects" class="projects section">
    <div class="container">
      <div class="section-header" data-aos="fade-up">
        <h2 class="section-title">Featured Projects</h2>
        <p class="section-subtitle">Some of my recent work</p>
      </div>

      <div class="projects-grid">
        <div class="project-card" data-aos="fade-up" data-aos-delay="100">
          <div class="project-image">
            <div class="project-overlay">
              <div class="project-links">
                <a href="#" class="project-link" aria-label="View Live Demo">
                  <i class="fas fa-external-link-alt"></i>
                </a>
                <a href="#" class="project-link" aria-label="View Source Code">
                  <i class="fab fa-github"></i>
                </a>
              </div>
            </div>
          </div>
          <div class="project-content">
            <div class="project-category">Full-Stack Web App</div>
            <h3 class="project-title">City Connect</h3>
            <p class="project-description">
              A full-stack problem-reporting platform where users can upload pictures and descriptions
              of local issues. Features include commenting, voting system, and real-time updates.
            </p>
            <div class="project-tech">
              <span class="tech-tag">Flask</span>
              <span class="tech-tag">MongoDB</span>
              <span class="tech-tag">JavaScript</span>
              <span class="tech-tag">HTML/CSS</span>
            </div>
          </div>
        </div>

        <div class="project-card" data-aos="fade-up" data-aos-delay="200">
          <div class="project-image">
            <div class="project-overlay">
              <div class="project-links">
                <a href="#" class="project-link" aria-label="View Live Demo">
                  <i class="fas fa-external-link-alt"></i>
                </a>
                <a href="#" class="project-link" aria-label="View Source Code">
                  <i class="fab fa-github"></i>
                </a>
              </div>
            </div>
          </div>
          <div class="project-content">
            <div class="project-category">MERN Stack</div>
            <h3 class="project-title">Airbnb Clone</h3>
            <p class="project-description">
              Full-stack Airbnb-like platform featuring listing creation, image uploads via Cloudinary,
              user authentication, and complete booking flow with payment integration.
            </p>
            <div class="project-tech">
              <span class="tech-tag">Node.js</span>
              <span class="tech-tag">Express.js</span>
              <span class="tech-tag">MongoDB</span>
              <span class="tech-tag">Cloudinary</span>
            </div>
          </div>
        </div>

        <div class="project-card" data-aos="fade-up" data-aos-delay="300">
          <div class="project-image">
            <div class="project-overlay">
              <div class="project-links">
                <a href="#" class="project-link" aria-label="View Live Demo">
                  <i class="fas fa-external-link-alt"></i>
                </a>
                <a href="#" class="project-link" aria-label="View Source Code">
                  <i class="fab fa-github"></i>
                </a>
              </div>
            </div>
          </div>
          <div class="project-content">
            <div class="project-category">AI-Powered App</div>
            <h3 class="project-title">Trip Zennie</h3>
            <p class="project-description">
              Travel assistant platform that auto-generates complete trip plans including routes,
              hotels, and transport based on minimal user inputs using AI and web scraping.
            </p>
            <div class="project-tech">
              <span class="tech-tag">Flask</span>
              <span class="tech-tag">Selenium</span>
              <span class="tech-tag">Gemini LLM</span>
              <span class="tech-tag">JavaScript</span>
            </div>
          </div>
        </div>

        <div class="project-card" data-aos="fade-up" data-aos-delay="400">
          <div class="project-image">
            <div class="project-overlay">
              <div class="project-links">
                <a href="#" class="project-link" aria-label="View Live Demo">
                  <i class="fas fa-external-link-alt"></i>
                </a>
                <a href="#" class="project-link" aria-label="View Source Code">
                  <i class="fab fa-github"></i>
                </a>
              </div>
            </div>
          </div>
          <div class="project-content">
            <div class="project-category">Healthcare App</div>
            <h3 class="project-title">Sahyog</h3>
            <p class="project-description">
              Doctor appointment booking system with Google Calendar integration and Firebase backend.
              Features real-time sync and modern UI. Won 3rd place in Postman API Hackathon.
            </p>
            <div class="project-tech">
              <span class="tech-tag">Firebase</span>
              <span class="tech-tag">Google Calendar API</span>
              <span class="tech-tag">Tailwind CSS</span>
              <span class="tech-tag">JavaScript</span>
            </div>
          </div>
        </div>
      </div>

      <div class="projects-cta" data-aos="fade-up" data-aos-delay="500">
        <p>Want to see more of my work?</p>
        <a href="#" class="btn btn-outline">
          <i class="fab fa-github"></i>
          View All Projects
        </a>
      </div>
    </div>
  </section>

  <!-- Achievements Section -->
  <section id="achievements" class="achievements section">
    <div class="container">
      <div class="section-header" data-aos="fade-up">
        <h2 class="section-title">Achievements & Milestones</h2>
        <p class="section-subtitle">My journey highlights</p>
      </div>

      <div class="timeline">
        <div class="timeline-item" data-aos="fade-right" data-aos-delay="100">
          <div class="timeline-marker">
            <i class="fas fa-trophy"></i>
          </div>
          <div class="timeline-content">
            <div class="timeline-date">2024</div>
            <h3>3rd Place – Postman API Hackathon</h3>
            <p>Secured 3rd position at BITS Pilani hackathon with Sahyog - a healthcare appointment booking system.</p>
            <div class="achievement-badge">🏆 Hackathon Winner</div>
          </div>
        </div>

        <div class="timeline-item" data-aos="fade-left" data-aos-delay="200">
          <div class="timeline-marker">
            <i class="fas fa-code"></i>
          </div>
          <div class="timeline-content">
            <div class="timeline-date">2024</div>
            <h3>300+ DSA Problems Solved</h3>
            <p>Consistently solved data structures and algorithms problems across LeetCode, CodeStudio, and other platforms.</p>
            <div class="achievement-badge">💻 Problem Solver</div>
          </div>
        </div>

        <div class="timeline-item" data-aos="fade-right" data-aos-delay="300">
          <div class="timeline-marker">
            <i class="fas fa-rocket"></i>
          </div>
          <div class="timeline-content">
            <div class="timeline-date">2024</div>
            <h3>Qualified 2 Rounds – Hack Heritage</h3>
            <p>Successfully qualified through multiple rounds of the prestigious Hack Heritage Hackathon.</p>
            <div class="achievement-badge">🚀 Qualifier</div>
          </div>
        </div>

        <div class="timeline-item" data-aos="fade-left" data-aos-delay="400">
          <div class="timeline-marker">
            <i class="fas fa-medal"></i>
          </div>
          <div class="timeline-content">
            <div class="timeline-date">2023-2024</div>
            <h3>SSB Conferences Participation</h3>
            <p>Attended Service Selection Board conferences at NDA Allahabad (33) and Bhopal (21), demonstrating leadership qualities.</p>
            <div class="achievement-badge">🎖️ Leadership</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="contact section">
    <div class="container">
      <div class="section-header" data-aos="fade-up">
        <h2 class="section-title">Get In Touch</h2>
        <p class="section-subtitle">Let's work together on your next project</p>
      </div>

      <div class="contact-content">
        <div class="contact-info" data-aos="fade-right">
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fas fa-envelope"></i>
            </div>
            <div class="contact-details">
              <h4>Email</h4>
              <p><EMAIL></p>
            </div>
          </div>

          <div class="contact-item">
            <div class="contact-icon">
              <i class="fas fa-phone"></i>
            </div>
            <div class="contact-details">
              <h4>Phone</h4>
              <p>+91 XXXXX XXXXX</p>
            </div>
          </div>

          <div class="contact-item">
            <div class="contact-icon">
              <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="contact-details">
              <h4>Location</h4>
              <p>Kolkata, West Bengal, India</p>
            </div>
          </div>

          <div class="contact-social">
            <h4>Follow Me</h4>
            <div class="social-links">
              <a href="#" class="social-link">
                <i class="fab fa-linkedin-in"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-github"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-instagram"></i>
              </a>
            </div>
          </div>
        </div>

        <div class="contact-form" data-aos="fade-left">
          <form id="contactForm" class="form">
            <div class="form-group">
              <input type="text" id="name" name="name" placeholder="Your Name" required>
              <label for="name">Your Name</label>
            </div>

            <div class="form-group">
              <input type="email" id="email" name="email" placeholder="Your Email" required>
              <label for="email">Your Email</label>
            </div>

            <div class="form-group">
              <input type="text" id="subject" name="subject" placeholder="Subject" required>
              <label for="subject">Subject</label>
            </div>

            <div class="form-group">
              <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
              <label for="message">Your Message</label>
            </div>

            <button type="submit" class="btn btn-primary btn-full">
              <i class="fas fa-paper-plane"></i>
              Send Message
            </button>
          </form>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-text">
          <p>&copy; 2025 Kaushal Raj Pandey. All rights reserved.</p>
          <p>Designed & Developed with <i class="fas fa-heart"></i> by Kaushal</p>
        </div>

        <div class="footer-links">
          <a href="#home">Home</a>
          <a href="#about">About</a>
          <a href="#projects">Projects</a>
          <a href="#contact">Contact</a>
        </div>
      </div>

      <div class="back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="script.js"></script>
</body>

</html>
